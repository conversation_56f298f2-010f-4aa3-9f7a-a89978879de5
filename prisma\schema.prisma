generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider  = "postgresql"
  url       = env("DATABASE_URL")
  directUrl = env("DIRECT_URL")
  schemas   = ["auth", "public"]
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
/// This model contains row level security and requires additional setup for migrations. Visit https://pris.ly/d/row-level-security for more info.
model audit_log_entries {
  instance_id String?   @db.Uuid
  id          String    @id @db.Uuid
  payload     Json?     @db.<PERSON><PERSON>
  created_at  DateTime? @db.Timestamptz(6)
  ip_address  String    @default("") @db.VarChar(64)

  @@index([instance_id], map: "audit_logs_instance_id_idx")
  @@schema("auth")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
/// This model contains row level security and requires additional setup for migrations. Visit https://pris.ly/d/row-level-security for more info.
model flow_state {
  id                     String                @id @db.Uuid
  user_id                String?               @db.Uuid
  auth_code              String
  code_challenge_method  code_challenge_method
  code_challenge         String
  provider_type          String
  provider_access_token  String?
  provider_refresh_token String?
  created_at             DateTime?             @db.Timestamptz(6)
  updated_at             DateTime?             @db.Timestamptz(6)
  authentication_method  String
  auth_code_issued_at    DateTime?             @db.Timestamptz(6)
  saml_relay_states      saml_relay_states[]

  @@index([created_at(sort: Desc)])
  @@index([auth_code], map: "idx_auth_code")
  @@index([user_id, authentication_method], map: "idx_user_id_auth_method")
  @@schema("auth")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
/// This model contains row level security and requires additional setup for migrations. Visit https://pris.ly/d/row-level-security for more info.
model identities {
  provider_id     String
  user_id         String    @db.Uuid
  identity_data   Json
  provider        String
  last_sign_in_at DateTime? @db.Timestamptz(6)
  created_at      DateTime? @db.Timestamptz(6)
  updated_at      DateTime? @db.Timestamptz(6)
  email           String?   @default(dbgenerated("lower((identity_data ->> 'email'::text))"))
  id              String    @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  users           users     @relation(fields: [user_id], references: [id], onDelete: Cascade, onUpdate: NoAction)

  @@unique([provider_id, provider], map: "identities_provider_id_provider_unique")
  @@index([email])
  @@index([user_id])
  @@schema("auth")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
/// This model contains row level security and requires additional setup for migrations. Visit https://pris.ly/d/row-level-security for more info.
model instances {
  id              String    @id @db.Uuid
  uuid            String?   @db.Uuid
  raw_base_config String?
  created_at      DateTime? @db.Timestamptz(6)
  updated_at      DateTime? @db.Timestamptz(6)

  @@schema("auth")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
/// This model contains row level security and requires additional setup for migrations. Visit https://pris.ly/d/row-level-security for more info.
model mfa_amr_claims {
  session_id            String   @db.Uuid
  created_at            DateTime @db.Timestamptz(6)
  updated_at            DateTime @db.Timestamptz(6)
  authentication_method String
  id                    String   @id(map: "amr_id_pk") @db.Uuid
  sessions              sessions @relation(fields: [session_id], references: [id], onDelete: Cascade, onUpdate: NoAction)

  @@unique([session_id, authentication_method], map: "mfa_amr_claims_session_id_authentication_method_pkey")
  @@schema("auth")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
/// This model contains row level security and requires additional setup for migrations. Visit https://pris.ly/d/row-level-security for more info.
model mfa_challenges {
  id                     String      @id @db.Uuid
  factor_id              String      @db.Uuid
  created_at             DateTime    @db.Timestamptz(6)
  verified_at            DateTime?   @db.Timestamptz(6)
  ip_address             String      @db.Inet
  otp_code               String?
  web_authn_session_data Json?
  mfa_factors            mfa_factors @relation(fields: [factor_id], references: [id], onDelete: Cascade, onUpdate: NoAction, map: "mfa_challenges_auth_factor_id_fkey")

  @@index([created_at(sort: Desc)], map: "mfa_challenge_created_at_idx")
  @@schema("auth")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
/// This model contains row level security and requires additional setup for migrations. Visit https://pris.ly/d/row-level-security for more info.
model mfa_factors {
  id                   String           @id @db.Uuid
  user_id              String           @db.Uuid
  friendly_name        String?
  factor_type          factor_type
  status               factor_status
  created_at           DateTime         @db.Timestamptz(6)
  updated_at           DateTime         @db.Timestamptz(6)
  secret               String?
  phone                String?
  last_challenged_at   DateTime?        @unique @db.Timestamptz(6)
  web_authn_credential Json?
  web_authn_aaguid     String?          @db.Uuid
  mfa_challenges       mfa_challenges[]
  users                users            @relation(fields: [user_id], references: [id], onDelete: Cascade, onUpdate: NoAction)

  @@unique([user_id, phone], map: "unique_phone_factor_per_user")
  @@index([user_id, created_at], map: "factor_id_created_at_idx")
  @@index([user_id])
  @@schema("auth")
}

/// This table contains check constraints and requires additional setup for migrations. Visit https://pris.ly/d/check-constraints for more info.
/// This model contains row level security and requires additional setup for migrations. Visit https://pris.ly/d/row-level-security for more info.
model one_time_tokens {
  id         String              @id @db.Uuid
  user_id    String              @db.Uuid
  token_type one_time_token_type
  token_hash String
  relates_to String
  created_at DateTime            @default(now()) @db.Timestamp(6)
  updated_at DateTime            @default(now()) @db.Timestamp(6)
  users      users               @relation(fields: [user_id], references: [id], onDelete: Cascade, onUpdate: NoAction)

  @@unique([user_id, token_type])
  @@index([relates_to], map: "one_time_tokens_relates_to_hash_idx", type: Hash)
  @@index([token_hash], map: "one_time_tokens_token_hash_hash_idx", type: Hash)
  @@schema("auth")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
/// This model contains row level security and requires additional setup for migrations. Visit https://pris.ly/d/row-level-security for more info.
model refresh_tokens {
  instance_id String?   @db.Uuid
  id          BigInt    @id @default(autoincrement())
  token       String?   @unique(map: "refresh_tokens_token_unique") @db.VarChar(255)
  user_id     String?   @db.VarChar(255)
  revoked     Boolean?
  created_at  DateTime? @db.Timestamptz(6)
  updated_at  DateTime? @db.Timestamptz(6)
  parent      String?   @db.VarChar(255)
  session_id  String?   @db.Uuid
  sessions    sessions? @relation(fields: [session_id], references: [id], onDelete: Cascade, onUpdate: NoAction)

  @@index([instance_id])
  @@index([instance_id, user_id])
  @@index([parent])
  @@index([session_id, revoked])
  @@index([updated_at(sort: Desc)])
  @@schema("auth")
}

/// This table contains check constraints and requires additional setup for migrations. Visit https://pris.ly/d/check-constraints for more info.
/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
/// This model contains row level security and requires additional setup for migrations. Visit https://pris.ly/d/row-level-security for more info.
model saml_providers {
  id                String        @id @db.Uuid
  sso_provider_id   String        @db.Uuid
  entity_id         String        @unique
  metadata_xml      String
  metadata_url      String?
  attribute_mapping Json?
  created_at        DateTime?     @db.Timestamptz(6)
  updated_at        DateTime?     @db.Timestamptz(6)
  name_id_format    String?
  sso_providers     sso_providers @relation(fields: [sso_provider_id], references: [id], onDelete: Cascade, onUpdate: NoAction)

  @@index([sso_provider_id])
  @@schema("auth")
}

/// This table contains check constraints and requires additional setup for migrations. Visit https://pris.ly/d/check-constraints for more info.
/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
/// This model contains row level security and requires additional setup for migrations. Visit https://pris.ly/d/row-level-security for more info.
model saml_relay_states {
  id              String        @id @db.Uuid
  sso_provider_id String        @db.Uuid
  request_id      String
  for_email       String?
  redirect_to     String?
  created_at      DateTime?     @db.Timestamptz(6)
  updated_at      DateTime?     @db.Timestamptz(6)
  flow_state_id   String?       @db.Uuid
  flow_state      flow_state?   @relation(fields: [flow_state_id], references: [id], onDelete: Cascade, onUpdate: NoAction)
  sso_providers   sso_providers @relation(fields: [sso_provider_id], references: [id], onDelete: Cascade, onUpdate: NoAction)

  @@index([created_at(sort: Desc)])
  @@index([for_email])
  @@index([sso_provider_id])
  @@schema("auth")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
/// This model contains row level security and requires additional setup for migrations. Visit https://pris.ly/d/row-level-security for more info.
model schema_migrations {
  version String @id @db.VarChar(255)

  @@schema("auth")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
/// This model contains row level security and requires additional setup for migrations. Visit https://pris.ly/d/row-level-security for more info.
model sessions {
  id             String           @id @db.Uuid
  user_id        String           @db.Uuid
  created_at     DateTime?        @db.Timestamptz(6)
  updated_at     DateTime?        @db.Timestamptz(6)
  factor_id      String?          @db.Uuid
  aal            aal_level?
  not_after      DateTime?        @db.Timestamptz(6)
  refreshed_at   DateTime?        @db.Timestamp(6)
  user_agent     String?
  ip             String?          @db.Inet
  tag            String?
  mfa_amr_claims mfa_amr_claims[]
  refresh_tokens refresh_tokens[]
  users          users            @relation(fields: [user_id], references: [id], onDelete: Cascade, onUpdate: NoAction)

  @@index([not_after(sort: Desc)])
  @@index([user_id])
  @@index([user_id, created_at], map: "user_id_created_at_idx")
  @@schema("auth")
}

/// This table contains check constraints and requires additional setup for migrations. Visit https://pris.ly/d/check-constraints for more info.
/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
/// This model contains row level security and requires additional setup for migrations. Visit https://pris.ly/d/row-level-security for more info.
/// This model contains an expression index which requires additional setup for migrations. Visit https://pris.ly/d/expression-indexes for more info.
model sso_domains {
  id              String        @id @db.Uuid
  sso_provider_id String        @db.Uuid
  domain          String
  created_at      DateTime?     @db.Timestamptz(6)
  updated_at      DateTime?     @db.Timestamptz(6)
  sso_providers   sso_providers @relation(fields: [sso_provider_id], references: [id], onDelete: Cascade, onUpdate: NoAction)

  @@index([sso_provider_id])
  @@schema("auth")
}

/// This table contains check constraints and requires additional setup for migrations. Visit https://pris.ly/d/check-constraints for more info.
/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
/// This model contains row level security and requires additional setup for migrations. Visit https://pris.ly/d/row-level-security for more info.
/// This model contains an expression index which requires additional setup for migrations. Visit https://pris.ly/d/expression-indexes for more info.
model sso_providers {
  id                String              @id @db.Uuid
  resource_id       String?
  created_at        DateTime?           @db.Timestamptz(6)
  updated_at        DateTime?           @db.Timestamptz(6)
  saml_providers    saml_providers[]
  saml_relay_states saml_relay_states[]
  sso_domains       sso_domains[]

  @@schema("auth")
}

/// This table contains check constraints and requires additional setup for migrations. Visit https://pris.ly/d/check-constraints for more info.
/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
/// This model contains row level security and requires additional setup for migrations. Visit https://pris.ly/d/row-level-security for more info.
/// This model contains an expression index which requires additional setup for migrations. Visit https://pris.ly/d/expression-indexes for more info.
model users {
  instance_id                 String?           @db.Uuid
  id                          String            @id @db.Uuid
  aud                         String?           @db.VarChar(255)
  role                        String?           @db.VarChar(255)
  email                       String?           @db.VarChar(255)
  encrypted_password          String?           @db.VarChar(255)
  email_confirmed_at          DateTime?         @db.Timestamptz(6)
  invited_at                  DateTime?         @db.Timestamptz(6)
  confirmation_token          String?           @db.VarChar(255)
  confirmation_sent_at        DateTime?         @db.Timestamptz(6)
  recovery_token              String?           @db.VarChar(255)
  recovery_sent_at            DateTime?         @db.Timestamptz(6)
  email_change_token_new      String?           @db.VarChar(255)
  email_change                String?           @db.VarChar(255)
  email_change_sent_at        DateTime?         @db.Timestamptz(6)
  last_sign_in_at             DateTime?         @db.Timestamptz(6)
  raw_app_meta_data           Json?
  raw_user_meta_data          Json?
  is_super_admin              Boolean?
  created_at                  DateTime?         @db.Timestamptz(6)
  updated_at                  DateTime?         @db.Timestamptz(6)
  phone                       String?           @unique
  phone_confirmed_at          DateTime?         @db.Timestamptz(6)
  phone_change                String?           @default("")
  phone_change_token          String?           @default("") @db.VarChar(255)
  phone_change_sent_at        DateTime?         @db.Timestamptz(6)
  confirmed_at                DateTime?         @default(dbgenerated("LEAST(email_confirmed_at, phone_confirmed_at)")) @db.Timestamptz(6)
  email_change_token_current  String?           @default("") @db.VarChar(255)
  email_change_confirm_status Int?              @default(0) @db.SmallInt
  banned_until                DateTime?         @db.Timestamptz(6)
  reauthentication_token      String?           @default("") @db.VarChar(255)
  reauthentication_sent_at    DateTime?         @db.Timestamptz(6)
  is_sso_user                 Boolean           @default(false)
  deleted_at                  DateTime?         @db.Timestamptz(6)
  is_anonymous                Boolean           @default(false)
  identities                  identities[]
  mfa_factors                 mfa_factors[]
  one_time_tokens             one_time_tokens[]
  sessions                    sessions[]
  patients                    patients[]
  profiles                    profiles?

  @@index([instance_id])
  @@index([is_anonymous])
  @@schema("auth")
}

/// This table contains check constraints and requires additional setup for migrations. Visit https://pris.ly/d/check-constraints for more info.
/// This model contains row level security and requires additional setup for migrations. Visit https://pris.ly/d/row-level-security for more info.
model appointments {
  id               String    @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  user_id          String    @db.Uuid
  patient_id       String?   @db.Uuid
  title            String
  description      String?
  appointment_date DateTime  @db.Date
  start_time       DateTime  @db.Time(6)
  end_time         DateTime  @db.Time(6)
  status           String    @default("scheduled")
  created_at       DateTime  @default(now()) @db.Timestamptz(6)
  updated_at       DateTime  @default(now()) @db.Timestamptz(6)
  patients         patients? @relation(fields: [patient_id], references: [id], onDelete: Cascade, onUpdate: NoAction)

  @@index([patient_id], map: "idx_appointments_patient")
  @@index([user_id, appointment_date], map: "idx_appointments_user_date")
  @@schema("public")
}

/// This model contains row level security and requires additional setup for migrations. Visit https://pris.ly/d/row-level-security for more info.
model consultation_summaries {
  id         String   @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  patient_id String   @db.Uuid
  date       DateTime @db.Date
  summary    String
  created_at DateTime @default(now()) @db.Timestamptz(6)
  updated_at DateTime @default(now()) @db.Timestamptz(6)
  patients   patients @relation(fields: [patient_id], references: [id], onDelete: Cascade, onUpdate: NoAction)

  @@schema("public")
}

/// This table contains check constraints and requires additional setup for migrations. Visit https://pris.ly/d/check-constraints for more info.
/// This model contains row level security and requires additional setup for migrations. Visit https://pris.ly/d/row-level-security for more info.
model patient_questionnaire_assignments {
  id               String    @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  patient_id       String    @db.Uuid
  questionnaire_id String    @db.Uuid
  assigned_by      String    @db.Uuid
  assigned_at      DateTime  @default(now()) @db.Timestamptz(6)
  completed_at     DateTime? @db.Timestamptz(6)
  status           String    @default("pending")
  created_at       DateTime  @default(now()) @db.Timestamptz(6)
  updated_at       DateTime  @default(now()) @db.Timestamptz(6)

  @@schema("public")
}

/// This model contains row level security and requires additional setup for migrations. Visit https://pris.ly/d/row-level-security for more info.
model patient_questionnaire_responses {
  id            String   @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  assignment_id String   @db.Uuid
  question_id   String   @db.Uuid
  response_text String
  created_at    DateTime @default(now()) @db.Timestamptz(6)
  updated_at    DateTime @default(now()) @db.Timestamptz(6)

  @@schema("public")
}

/// This model contains row level security and requires additional setup for migrations. Visit https://pris.ly/d/row-level-security for more info.
model patients {
  id                     String                   @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  user_id                String                   @db.Uuid
  name                   String
  cpf                    String
  phone                  String
  main_complaints        String?
  anamnesis              String?
  created_at             DateTime                 @default(now()) @db.Timestamptz(6)
  updated_at             DateTime                 @default(now()) @db.Timestamptz(6)
  appointments           appointments[]
  consultation_summaries consultation_summaries[]
  users                  users                    @relation(fields: [user_id], references: [id], onDelete: Cascade, onUpdate: NoAction)

  @@schema("public")
}

/// This model contains row level security and requires additional setup for migrations. Visit https://pris.ly/d/row-level-security for more info.
model profiles {
  id          String   @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  user_id     String   @unique @db.Uuid
  doctor_name String   @default("Dr. João Silva")
  clinic_name String   @default("PsicoSys")
  crp         String   @default("CRP 06/12345")
  phone       String   @default("(11) 99999-9999")
  email       String
  created_at  DateTime @default(now()) @db.Timestamptz(6)
  updated_at  DateTime @default(now()) @db.Timestamptz(6)
  users       users    @relation(fields: [user_id], references: [id], onDelete: Cascade, onUpdate: NoAction)

  @@schema("public")
}

/// This model contains row level security and requires additional setup for migrations. Visit https://pris.ly/d/row-level-security for more info.
model questionnaire_questions {
  id               String         @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  questionnaire_id String         @db.Uuid
  question_text    String
  question_order   Int            @default(1)
  created_at       DateTime       @default(now()) @db.Timestamptz(6)
  questionnaires   questionnaires @relation(fields: [questionnaire_id], references: [id], onDelete: Cascade, onUpdate: NoAction)

  @@schema("public")
}

/// This model contains row level security and requires additional setup for migrations. Visit https://pris.ly/d/row-level-security for more info.
model questionnaires {
  id                      String                    @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  user_id                 String                    @db.Uuid
  title                   String
  description             String?
  created_at              DateTime                  @default(now()) @db.Timestamptz(6)
  updated_at              DateTime                  @default(now()) @db.Timestamptz(6)
  questionnaire_questions questionnaire_questions[]

  @@schema("public")
}

enum aal_level {
  aal1
  aal2
  aal3

  @@schema("auth")
}

enum code_challenge_method {
  s256
  plain

  @@schema("auth")
}

enum factor_status {
  unverified
  verified

  @@schema("auth")
}

enum factor_type {
  totp
  webauthn
  phone

  @@schema("auth")
}

enum one_time_token_type {
  confirmation_token
  reauthentication_token
  recovery_token
  email_change_token_new
  email_change_token_current
  phone_change_token

  @@schema("auth")
}
